const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

const {
  AdminAddUserToGroupCommand,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  AdminUpdateUserAttributesCommand,
  AdminDisableUserCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider');

const cognitoClient = new CognitoIdentityProviderClient({});

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  console.log('💬 log of params : ', params)

  const header = e.headers;
  const base = new Base(pool, params.languageCode);
  let tenant = null;

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base.checkOrigin(header.origin || header.Origin).then(data => {
        tenant = data;
      });
    })
    .then(() => {
      console.log('INPUT VALIDATION');
      const registerData = params.registerData;
      const checkData = Object.assign({}, registerData);

      return Promise.resolve()
        .then(() => {
          console.log('Check Duplicated Email');
          console.log('Email: ', checkData.email);
          checkData.emailDuplicated = 0;
          if (checkData?.email) {
            return pool
              .rlsQuery(
                tenant.tenant_no,
                'SELECT * FROM "f_get_member_by_email"($1,$2);', [
                tenant.tenant_no,
                checkData.email,
              ])
              .then(members => {
                console.log('members: ', members);
                if (members && members.length > 0) {
                  checkData.emailDuplicated = -1;

                  return Promise.reject({
                    status  : 400,
                    errors : [{
                      email: `メールアドレスがすでに登録されています。`
                    }],
                  })
                }
                return Promise.resolve();
              });
          }
          return Promise.resolve();
        })
        .then(() => {
          const sqlParams = [tenant.tenant_no, 'member', params.languageCode];
          return pool.rlsQuery(
                        tenant.tenant_no,
                        'SELECT * FROM "f_get_field_list"($1,$2,$3);',
                        sqlParams
                      )
        })
        .then((result) => {
        console.log('result', result)
        const freeField = params.registerData;
        const errorList = []
        result.forEach(field => {
          if (field.required_flag && !freeField[field.physical_name]) {
            const error = {}
            error[field.physical_name] = `${field.logical_name}を入力してください。`
            errorList.push(error)
          }
          if (field.max_length && freeField[field.physical_name] && String(freeField[field.physical_name]).length > field.max_length) {
            const error = {}
            error[field.physical_name] = `${field.logical_name}は${field.max_length}桁以内で入力してください。`
            errorList.push(error)
          }
          if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
            const error = {}
            error[field.physical_name] = `${field.logical_name}は最大${field.max_value}まで入力してください。`
            errorList.push(error)
          }
          if (field.regular_expressions && freeField[field.physical_name]) {
            console.log(field.regular_expressions, freeField[field.physical_name])
            const regex = new RegExp(String(field.regular_expressions))
            if (field.input_type === 'file') {
              freeField[field.physical_name].forEach(file => {
                console.log('file', file, regex.test(file))
                if(!regex.test(file)) {
                  const error = {}
                  error[field.physical_name] = `${field.logical_name}の「${file}」は正規表現と一致しません。`
                  errorList.push(error)
                }
              })
            } else {
              if (!(new RegExp(freeField[field.regular_expressions])).test(field.input_type)) {
                const error = {}
                error[field.physical_name] = `${field.logical_name}は正規表現と一致しません。`
                errorList.push(error)
              }
            }
          }
        });
        if (errorList.length > 0) return Promise.reject({
          status  : 400,
          errors : errorList,
        })
        return Promise.resolve()
      })
    })
    .then(() => {
      console.log('INSERT MEMBER REQUEST');
      if (params.validateFlag) {
        return Promise.resolve({});
      }

      let tokenConstant = {};
      return Promise.resolve()
        .then(() => {
          // Get token constants
          return pool.rlsQuery(
                        tenant.tenant_no,
                        'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                        [tenant.tenant_no,['MEMBER_REQUEST_TOKEN_EXPIRED'],base.language]
                      )
            .then(data => {
              tokenConstant =
                data.find(
                  content =>
                    content.key_string === 'MEMBER_REQUEST_TOKEN_EXPIRED'
                ) || {};
              return Promise.resolve();
            });
        })
        .then(() => {
          if (params.registerData.password) {
            return base.hashPassword(params.registerData.password)
            .then((hash) => {
              const freeField = params.registerData;
              freeField.password = hash
              delete freeField.confirm_password
              return Promise.resolve(freeField)
            })
          }

        })
        .then(freeField => {
          // Get token, token expired info
          const now = new Date();
          let tokenExpiredDate = null;
          tokenString = null;
          if (tokenConstant.value1) {
            const expiredPitch = Number(tokenConstant.value1);
            tokenExpiredDate = new Date();
            tokenExpiredDate.setHours(now.getHours() + expiredPitch);
            tokenString = Common.randomNumber(6);
          }

          return pool.rlsQuery(tenant.tenant_no,
            'SELECT * FROM "f_insert_member_request"($1,$2,$3);',
            [
              tenant.tenant_no,
              1,
              {
                ...freeField,
                lang: base.language,
              },
            ]
          );
        })
        .then(createdUserInfo => {
          console.log('CREATING COGNITO USER');
          console.log('Member request created with ID:', createdUserInfo);

          // Get user information from params (original request data)
          const email = params.registerData.email;
          const memberName = params.registerData.memberName || params.registerData.member_name || '';
          const languageCode = params.languageCode || 'ja';
          const password = params.registerData.password;

          let cognitoUsername = null;
          let createdMember = null;

          // Step 1: Create Cognito user
          return Promise.resolve()
            .then(() => {
              console.log('Step 1: Creating Cognito user...');
              const command = new AdminCreateUserCommand({
                UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
                Username: email,
                UserAttributes: [
                  {Name: 'email', Value: email},
                  {Name: 'email_verified', Value: 'true'},
                  {Name: 'custom:member_name', Value: memberName},
                ],
                TemporaryPassword: password,
                MessageAction: 'SUPPRESS',
              });

              return cognitoClient.send(command)
                .then(createUserResult => {
                  console.log('✓ Cognito user created successfully:', createUserResult);
                  cognitoUsername = createUserResult.User.Username;
                  return Promise.resolve();
                });
            })
            // Step 2: Set permanent password
            .then(() => {
              console.log('Step 2: Setting permanent password...');
              const command = new AdminSetUserPasswordCommand({
                UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
                Username: cognitoUsername,
                Password: password,
                Permanent: true,
              });
              return cognitoClient.send(command)
                .then(() => {
                  console.log('✓ Password set to permanent');
                  return Promise.resolve();
                });
            })
            // Step 3: Add user to tenant group
            .then(() => {
              console.log('Step 3: Adding user to tenant group...');
              const groupName = `tenant-id:${tenant.tenant_no}`;
              const command = new AdminAddUserToGroupCommand({
                UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
                Username: cognitoUsername,
                GroupName: groupName,
              });
              return cognitoClient.send(command)
                .then(() => {
                  console.log(`✓ User added to tenant group: ${groupName}`);
                  return Promise.resolve();
                });
            })
        })
        // Send emails to member and admin
        .then(() => {
          console.log('Step 6: Sending confirmation emails...');
          const member = params.registerData;

          return Promise.all([
            Promise.resolve()
              .then(() => {
                const language = params.registerData.emailLang || 'ja';
                return pool.rlsQuery(
                  tenant.tenant_no,
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    [
                      'EMAIL_MEMBER_REQUEST_FOR_MEMBER',
                      'EMAIL_COMMON_FOOTER',
                      'EMAIL_FROM',
                    ],
                    language,
                  ]
                );
              })
              .then(constants => {
                const footer =
                  constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') ||
                  {};
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const mail =
                  constants.find(
                    x => x.key_string === 'EMAIL_MEMBER_REQUEST_FOR_MEMBER'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = [member.email];
                const bcc = mail.value3 ? mail.value3.split(',') : [];
                const content = Common.format(mail.value4, [footer.value4]);
                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
            Promise.resolve()
              .then(() => {
                const language = 'ja';
                return pool.rlsQuery(
                  tenant.tenant_no,
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    ['EMAIL_MEMBER_REQUEST_FOR_ADMIN', 'EMAIL_FROM'],
                    language,
                  ]
                );
              })
              .then(constants => {
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const mail =
                  constants.find(
                    x => x.key_string === 'EMAIL_MEMBER_REQUEST_FOR_ADMIN'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = mail.value3 ? mail.value3.split(',') : [];
                const bcc = [];

                // Tel
                const tel = member.telCountryCode
                  ? `${member.telCountryCode} ${member.tel}`
                  : member.tel;

                const content = Common.format(mail.value4, [
                  member.companyName || '',
                  member.companyName || '',
                  tel || '',
                  member.email || '',
                ]);
                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
          ]).then(() => {
            console.log('✓ Confirmation emails sent successfully');
            return Promise.resolve({
              message: 'Member registration completed successfully',
              email: member.email,
              tenantId: tenant.tenant_no,
            });
          });
        });
    })
    .then(data => {
      console.log('☎️ log of final resolved data : ', data)
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
