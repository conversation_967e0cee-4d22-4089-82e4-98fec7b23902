module "vpc" {
  source               = "./vpc"
  project_name         = var.project_name
  environment          = var.environment
  s3-bucket-arn        = var.s3-bucket-arn
  s3-bucket-id         = var.s3-bucket-id
  nat_gateway_amount   = var.nat_gateway_amount
  external_domain_name = var.external_domain_name
  record_name          = var.record_name
}

module "ec2" {
  source                = "./ec2"
  project_name          = var.project_name
  environment           = var.environment
  ec2_security_group_id = module.vpc.ec2_security_group_id
  ec2_subnet_id         = module.vpc.ec2_subnet_id
}


module "rds-aurora" {
  source                           = "./rds-aurora"
  project_name                     = var.project_name
  environment                      = var.environment
  serverless_min_capacity          = var.serverless_min_capacity
  serverless_max_capacity          = var.serverless_max_capacity
  enable_read_replica              = var.enable_read_replica
  database_security_group_ids      = [module.vpc.database_security_group_id]
  database_subnet_ids              = module.vpc.database_subnet_ids
  proxies_security_group_ids       = [module.vpc.rds_proxy_security_group_id]
  proxies_subnet_ids               = module.vpc.rds_proxy_subnet_ids
  slack-notification-sns-topic-arn = module.sns-event.system_error_sns_topic_arn
}

module "admin-cognito" {
  source        = "./admin-cognito"
  project_name  = var.project_name
  environment   = var.environment
  domain_prefix = "${var.project_name}-${var.environment}"
  # TODO user_pool_name not use?
  user_pool_name           = "${var.project_name}-${var.environment}-user-pool"
  auto_verified_attributes = ["email"]

  # パスワードポリシー(すべてのテナントに適用する)
  password_policy = var.password_policy
  tenant_ids      = [0, 1, 2, 3, 4, 5] # 0: system-admin, 1: tenant 1, 2: tenant 2, 3: tenant 3 ...
  app_clients = [
    {
      name                   = "saas-client"
      generate_secret        = false
      refresh_token_validity = 30
      access_token_validity  = 1
      id_token_validity      = 1
      allowed_oauth_flows    = ["code", "implicit"]
      callback_urls = [
        "https://${var.admin_domain_name}/callback"
      ]
      logout_urls = [
        "https://${var.admin_domain_name}/logout"
      ]
      allowed_oauth_scopes = ["email", "openid", "profile"]
    }
  ]
}

# Separate Cognito User Pool for auction site
module "auction-cognito" {
  source        = "./auction-cognito"
  project_name  = var.project_name
  environment   = var.environment
  domain_prefix = "${var.project_name}-${var.environment}"

  # Password policy (same as admin)
  password_policy = var.password_policy
  tenant_ids      = ["0", "1", "2", "3", "4", "5"] # 0: system-admin, 1: tenant 1, 2: tenant 2, 3: tenant 3 ...

  callback_urls = [
    "https://${var.auction_domain_name}/callback",
    "https://${var.auction_domain_name}/login/callback"
  ]
  logout_urls = [
    "https://${var.auction_domain_name}/logout",
    "https://${var.auction_domain_name}/login"
  ]
}

module "auction-side" {
  source                                  = "./auction-side"
  project_name                            = var.project_name
  environment                             = var.environment
  external_domain_name                    = var.external_domain_name
  record_name                             = var.record_name
  domain_name                             = var.auction_domain_name
  mail_from_domain                        = var.mail_from_domain
  s3-bucket-arn                           = var.s3-bucket-arn
  s3-bucket-id                            = var.s3-bucket-id
  s3-bucket-bucket                        = var.s3-bucket-bucket
  bucket_regional_domain_name             = var.bucket_regional_domain_name
  slack-notification-sns-topic-arn        = module.sns-event.system_error_sns_topic_arn
  lambda_subnet_ids                       = module.vpc.lambda_with_ngw_subnets_ids
  lambda_security_group_id                = module.vpc.lambda_security_group_id
  slack-notification-lambda-arn           = module.sns-event.system_error_endpoint_lambda_arn
  convert_xlsx_to_pdf_endpoint_lambda_arn = module.batch.convert_xlsx_to_pdf_endpoint_lambda_arn
  # Use auction-specific Cognito User Pool
  cognito_user_pool_arn = module.auction-cognito.user_pool_arn
  cognito_user_pool_id  = module.auction-cognito.user_pool_id
  cognito_client_id     = module.auction-cognito.app_client_id
  lambda_global_environment_variables = merge(
    var.auction_lambda_global_environment_variables,
    {
      "PGUSER" : module.rds-aurora.master_username,
      "PGHOST" : module.rds-aurora.proxy_endpoint,
      "READ_ONLY_PGHOST" : module.rds-aurora.proxy_read_only_endpoint,
      "PGDATABASE" : module.rds-aurora.database_name,
      "PGPASSWORD" : module.rds-aurora.master_password,
      "PGPORT" : module.rds-aurora.port,
      "S3_BUCKET" : var.s3-bucket-id,
      "NOTICE_SLACK_FUNCTION" : module.sns-event.system_error_endpoint_lambda_arn,
      "WEB_SOCKET_ENDPOINT" : var.web_socket_domain,
      # Auction-specific Cognito configuration
      "AUCTION_COGNITO_USER_POOL_ID" : module.auction-cognito.user_pool_id,
      "AUCTION_COGNITO_CLIENT_ID" : module.auction-cognito.app_client_id,
      "AUCTION_DOMAIN_NAME" : var.auction_domain_name,
    }
  )
  common_lambda_layer     = module.nodejs-lib.nodejs_lib_layer_arn
  libreoffice_lib_layer   = module.libreoffice.layer_arn
  profile_name            = var.profile_name
  basic_auth_enable       = var.basic_auth_enable
  auction_tenants         = var.auction_tenants
  auction_custom_domains  = var.auction_custom_domains
  auction_acm_domain_name = var.auction_acm_domain_name
  manual_deploy           = var.manual_deploy
  mail_domains            = var.mail_domains
}

module "admin-side" {
  source                             = "./admin-side"
  project_name                       = var.project_name
  environment                        = var.environment
  external_domain_name               = var.external_domain_name
  record_name                        = var.record_name
  domain_name                        = var.admin_domain_name
  s3-bucket-arn                      = var.s3-bucket-arn
  s3-bucket-id                       = var.s3-bucket-id
  s3-bucket-bucket                   = var.s3-bucket-bucket
  bucket_regional_domain_name        = var.bucket_regional_domain_name
  slack-notification-sns-topic-arn   = module.sns-event.system_error_sns_topic_arn
  lambda_subnet_ids                  = module.vpc.lambda_with_ngw_subnets_ids
  lambda_security_group_id           = module.vpc.lambda_security_group_id
  slack-notification-lambda-arn      = module.sns-event.system_error_endpoint_lambda_arn
  sent-html-email-lambda-arn         = module.batch.sent-html-email-lambda-arn
  sent-html-notice-email-lambda-arn  = module.batch.sent-html-notice-email-lambda-arn
  zip-transfer-lambda-arn            = module.s3-event.zip-transfer-lambda-arn
  get-first-frame-mp4-lambda-arn     = module.s3-event.get-first-frame-mp4-lambda-arn
  send-notification-email-lambda-arn = module.batch.send-notification-email-lambda-arn
  push_change_items_lambda_arn       = module.auction-side.push_change_items_lambda_arn
  lambda_global_environment_variables = merge(
    var.admin_lambda_global_environment_variables,
    {
      "PGUSER" : module.rds-aurora.master_username,
      "PGHOST" : module.rds-aurora.proxy_endpoint,
      "READ_ONLY_PGHOST" : module.rds-aurora.proxy_read_only_endpoint,
      "PGDATABASE" : module.rds-aurora.database_name,
      "PGPASSWORD" : module.rds-aurora.master_password,
      "PGPORT" : module.rds-aurora.port,
      "S3_BUCKET" : var.s3-bucket-id,
      "NOTICE_SLACK_FUNCTION" : module.sns-event.system_error_endpoint_lambda_arn,
      "AUCTION_USER_POOL_ID_IN_ADMIN" : module.auction-cognito.user_pool_id,
      "AUCTION_CLIENT_ID_IN_ADMIN" : module.auction-cognito.app_client_id
    }
  )
  common_lambda_layer     = module.nodejs-lib.nodejs_lib_layer_arn
  profile_name            = var.profile_name
  admin_basic_auth_enable = var.admin_basic_auth_enable
  cognito_user_pool_arn   = module.admin-cognito.user_pool_arn
  cognito_user_pool_id    = module.admin-cognito.user_pool_id
  cognito_client_id       = module.admin-cognito.app_client_ids_map["saas-client"] # use for login gateway resource
  auction_cognito_user_pool_id = module.auction-cognito.user_pool_id
  admin_tenants           = var.admin_tenants
  admin_custom_domains    = var.admin_custom_domains
  admin_acm_domain_name   = var.admin_acm_domain_name
  manual_deploy           = var.manual_deploy
}

module "s3-event" {
  source                   = "./s3-event"
  project_name             = var.project_name
  environment              = var.environment
  s3-bucket-arn            = var.s3-bucket-arn
  s3-bucket-id             = var.s3-bucket-id
  lambda_subnet_ids        = module.vpc.lambda_with_ngw_subnets_ids
  lambda_security_group_id = module.vpc.lambda_security_group_id
  lambda_global_environment_variables = {
    "PGUSER" : module.rds-aurora.master_username,
    "PGHOST" : module.rds-aurora.proxy_endpoint,
    "READ_ONLY_PGHOST" : module.rds-aurora.proxy_read_only_endpoint,
    "PGDATABASE" : module.rds-aurora.database_name,
    "PGPASSWORD" : module.rds-aurora.master_password,
    "PGPORT" : module.rds-aurora.port,
    "S3_BUCKET" : var.s3-bucket-id
  }
  common_lambda_layer           = module.nodejs-lib.nodejs_lib_layer_arn
  slack-notification-lambda-arn = module.sns-event.system_error_endpoint_lambda_arn
}

module "sns-event" {
  source                   = "./sns-event"
  project_name             = var.project_name
  environment              = var.environment
  lambda_subnet_ids        = module.vpc.lambda_with_ngw_subnets_ids
  lambda_security_group_id = module.vpc.lambda_security_group_id
  slack_hook_url           = var.api_gateway_5xx_alarm_slack_hook_url
  lambda_global_environment_variables = {
    "PGUSER" : module.rds-aurora.master_username,
    "PGHOST" : module.rds-aurora.proxy_endpoint,
    "READ_ONLY_PGHOST" : module.rds-aurora.proxy_read_only_endpoint,
    "PGDATABASE" : module.rds-aurora.database_name,
    "PGPASSWORD" : module.rds-aurora.master_password,
    "PGPORT" : module.rds-aurora.port
  }
  common_lambda_layer = module.nodejs-lib.nodejs_lib_layer_arn
}

module "batch" {
  source                   = "./batch"
  project_name             = var.project_name
  environment              = var.environment
  s3-bucket-arn            = var.s3-bucket-arn
  s3-bucket-id             = var.s3-bucket-id
  lambda_subnet_ids        = module.vpc.lambda_with_ngw_subnets_ids
  lambda_security_group_id = module.vpc.lambda_security_group_id
  lambda_global_environment_variables = {
    "PGUSER" : module.rds-aurora.master_username,
    "PGHOST" : module.rds-aurora.proxy_endpoint,
    "READ_ONLY_PGHOST" : module.rds-aurora.proxy_read_only_endpoint,
    "PGDATABASE" : module.rds-aurora.database_name,
    "PGPASSWORD" : module.rds-aurora.master_password,
    "PGPORT" : module.rds-aurora.port,
    "S3_BUCKET" : var.s3-bucket-id,
    "AUCTION_DOMAIN_NAME" : var.auction_domain_name,
  }
  common_lambda_layer           = module.nodejs-lib.nodejs_lib_layer_arn
  slack-notification-lambda-arn = module.sns-event.system_error_endpoint_lambda_arn
  batch-cron-rule               = var.batch-cron-rule
  libreoffice_lib_layer         = module.libreoffice.layer_arn
}

module "nodejs-lib" {
  source       = "./nodejs-lib"
  project_name = var.project_name
  environment  = var.environment
}

module "libreoffice" {
  source       = "./libreoffice"
  project_name = var.project_name
  environment  = var.environment
  s3-bucket-id = var.s3-bucket-id
}

# module "web-socket" {
#   source                        = "./web-socket"
#   project_name                  = var.project_name
#   environment                   = var.environment
#   record_name                   = var.record_name
#   external_domain_name          = var.external_domain_name
#   domain_name                   = var.web_socket_domain
#   lambda_subnet_ids             = module.vpc.lambda_with_ngw_subnets_ids
#   lambda_security_group_id      = module.vpc.lambda_security_group_id
#   slack-notification-lambda-arn = module.sns-event.system_error_endpoint_lambda_arn
#   lambda_global_environment_variables = merge(
#     {
#       "ADMIN_JWT_KEY" : var.admin_lambda_global_environment_variables.JWT_KEY,
#       "AUCTION_JWT_KEY" : var.auction_lambda_global_environment_variables.JWT_KEY,
#       "PGUSER" : module.rds-aurora.master_username,
#       "PGHOST" : module.rds-aurora.proxy_endpoint,
#       "READ_ONLY_PGHOST" : module.rds-aurora.proxy_read_only_endpoint,
#       "PGDATABASE" : module.rds-aurora.database_name,
#       "PGPASSWORD" : module.rds-aurora.master_password,
#       "PGPORT" : module.rds-aurora.port,
#       "NOTICE_SLACK_FUNCTION" : module.sns-event.system_error_endpoint_lambda_arn
#     }
#   )
#   common_lambda_layer = module.nodejs-lib.nodejs_lib_layer_arn
#   profile_name        = var.profile_name
# }
