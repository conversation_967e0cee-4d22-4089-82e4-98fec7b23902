variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "external_domain_name" {
  description = "external_domain_name"
}

variable "record_name" {
  description = "record_name"
  default = ""
}

# TODO: domain_nameが使わない
variable "domain_name" {
  description = "domain_name"
}

variable "s3-bucket-arn" {
  description = "File upload bucket arn"
}

variable "s3-bucket-id" {
  description = "File upload bucket id"
}

variable "bucket_regional_domain_name" {
  description = "File upload bucket bucket_regional_domain_name"
}

variable "api_gateway_stage_name" {
  description = "api_gateway_stage_name"
  default = "api"
}



variable "slack-notification-sns-topic-arn" {
  description = "slack-notification-sns-topic-arn"
}

variable "s3-bucket-bucket" {
  description = "File upload bucket bucket"
}

variable "api_gateway_5xx_alarm_slack_hook_url" {
  description = "api_gateway_5xx_alarm_slack_hook_url"
  default = ""
}

variable "lambda_subnet_ids" {
  description = "lambda_subnet_ids"
  default       = []
}

variable "lambda_security_group_id" {
  description = "lambda_security_group_id"
  default     = ""
}

variable "lambda_global_environment_variables" {
  description = "lambda_global_environment_variables"
  default     = {}
}

variable "slack-notification-lambda-arn" {
  description = "slack-notification-lambda-arn"
}

variable "common_lambda_layer" {
  description = "common_lambda_layer"
}

variable "sent-html-email-lambda-arn" {
  description = "sent-html-email-lambda-arn"
}

variable "sent-html-notice-email-lambda-arn" {
  description = "sent-html-notice-email-lambda-arn"
}

variable "profile_name" {
  description = "profile_name"
}

variable "zip-transfer-lambda-arn" {
  description = "zip-transfer-lambda-arn"
}

variable "get-first-frame-mp4-lambda-arn" {
  description = "get-first-frame-mp4-lambda-arn"
}

variable "send-notification-email-lambda-arn" {
  description = "send-notification-email-lambda-arn"
}

variable "push_change_items_lambda_arn" {
  description = "push_change_items_lambda_arn"
}

variable "admin_basic_auth_enable" {
  description = "admin_basic_auth_enable"
}

variable "cognito_user_pool_arn" {
  description = "arn of cognito user pool, use for cognito authorizer"
  type        = string
}

variable "cognito_user_pool_id" {
  description = "ID of cognito user pool, used to validate JWT tokens"
  type        = string
}

variable "cognito_client_id" {
  description = "ID of Cognito App Client"
  type        = string
}

variable "auction_cognito_user_pool_id" {
  description = "ID of auction cognito user pool, used for member management operations"
  type        = string
}

variable "admin_tenants" {
  description = "admin_tenants"
  type = map(object({
    tenant_id        = string
    use_custom_domain = bool
    domain_name       = string
    hosted_zone_name  = string
  }))
}

variable "admin_custom_domains" {
  description = "admin_custom_domains"
  type = list(string)
}

variable "admin_acm_domain_name" {
  description = "admin_acm_domain_name"
  type = string
}

variable "manual_deploy" {
  description = "manual_deploy"
  type        = bool
  default     = false
}
