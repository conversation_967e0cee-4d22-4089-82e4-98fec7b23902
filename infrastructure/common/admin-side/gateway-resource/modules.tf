module "get-aws-credentials" {
  source = "./get-aws-credentials"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "create-notice" {
  source = "./create-notice"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "edit-notice" {
  source = "./edit-notice"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-notice-list" {
  source = "./get-notice-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-notice-by-notice-no" {
  source = "./get-notice-by-notice-no"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "delete-notice" {
  source = "./delete-notice"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-constant-list" {
  source = "./get-constant-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-constants-language-list" {
  source = "./get-constants-language-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "regist-constants-language-list" {
  source = "./regist-constants-language-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}
module "download-file" {
  source = "./download-file"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "delete-constant" {
  source = "./delete-constant"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibitions" {
  source = "./get-exhibitions"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "upsert-exhibitions" {
  source = "./upsert-exhibitions"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  zip-transfer-lambda-arn =  var.zip-transfer-lambda-arn
}

module "delete-exhibitions" {
  source = "./delete-exhibitions"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-constants-by-keys" {
  source = "./get-constants-by-keys"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-email-notification-list" {
  source = "./get-email-notification-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-admin-list" {
  source = "./get-admin-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-admin-by-admin-no" {
  source = "./get-admin-by-admin-no"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-use-bounces-list" {
  source = "./get-use-bounces-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "create-admin" {
  source = "./create-admin"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  cognito_user_pool_id = var.cognito_user_pool_id
}

module "edit-admin" {
  source = "./edit-admin"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  cognito_user_pool_id = var.cognito_user_pool_id
}

// 公開API（PUBLIC）
module "admin-confirm-register-user" {
  source = "./admin-confirm-register-user"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  # aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id(公開API GatewayのAuthorizerを使用しないため、コメントアウト)
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  cognito_user_pool_id = var.cognito_user_pool_id
}

module "get-constant-key" {
  source = "./get-constant-key"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-lots" {
  source = "./get-lots"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-members" {
  source = "./get-members"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-member" {
  source = "./get-member"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "update-member-status" {
  source = "./update-member-status"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-member-status-history" {
  source = "./get-member-status-history"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "update-member" {
  source = "./update-member"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibition-email-language-list" {
  source = "./get-exhibition-email-language-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-constants-by-keys-language" {
  source = "./get-constants-by-keys-language"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}


module "regist-exhibition-mail-language-list" {
  source = "./regist-exhibition-mail-language-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "cancel-exhibition-item" {
  source = "./cancel-exhibition-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-items" {
  source = "./get-items"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-item-detail" {
  source = "./get-item-detail"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "export-member-csv-file" {
  source = "./export-member-csv-file"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "import-member-csv-file" {
  source = "./import-member-csv-file"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  update-member-status-lambda-function-arn = module.update-member-status.lambda_function_arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibition-status-list" {
  source = "./get-exhibition-status-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-bid-history" {
  source = "./get-bid-history"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-bid-order" {
  source = "./get-bid-order"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-exhibition-by-name" {
  source = "./get-exhibition-by-name"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "export-inquiry-csv-file" {
  source = "./export-inquiry-csv-file"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-bid-result" {
  source = "./get-bid-result"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-dashboard" {
  source = "./get-dashboard"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibition-pulldown" {
  source = "./get-exhibition-pulldown"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibition-information" {
  source = "./get-exhibition-information"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibition-name-pulldown" {
  source = "./get-exhibition-name-pulldown"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibition-summary" {
  source = "./get-exhibition-summary"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "sent-email" {
  source = "./sent-email"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  sent-html-email-lambda-arn = var.sent-html-email-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "create-notice-email" {
  source = "./create-notice-email"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "edit-notice-email" {
  source = "./edit-notice-email"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-notice-email-list" {
  source = "./get-notice-email-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-notice-email-by-notice-email-no" {
  source = "./get-notice-email-by-notice-email-no"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "delete-notice-email" {
  source = "./delete-notice-email"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "sent-notice-email" {
  source = "./sent-notice-email"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  sent-html-notice-email-lambda-arn = var.sent-html-notice-email-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "search-exhibitions" {
  source = "./search-exhibitions"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "upsert-item" {
  source = "./upsert-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  get-first-frame-mp4-lambda-arn = var.get-first-frame-mp4-lambda-arn
}

module "delete-exhibition-item" {
  source = "./delete-exhibition-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "export-items-xlsx-file" {
  source = "./export-items-xlsx-file"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "republish-exhibition-item" {
  source = "./republish-exhibition-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "create-member" {
  source = "./create-member"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}
module "get-tenant-credit-remaining" {
  source = "./get-tenant-credit-remaining"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}
module "upsert-tenant-credit-remaining" {
  source = "./upsert-tenant-credit-remaining"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-tenant-setting" {
  source = "./get-tenant-setting"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "update-tenant-setting" {
  source = "./update-tenant-setting"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-field-list" {
  source = "./get-field-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-tenant-language-list" {
  source = "./get-tenant-language-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}
module "regist-field-item" {
  source = "./regist-field-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}
module "get-field-item" {
  source = "./get-field-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "delete-field-item" {
  source = "./delete-field-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "check-field-item-used" {
  source = "./check-field-item-used"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-display-field-list" {
  source = "./get-display-field-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-field-all-list" {
  source = "./get-field-all-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "regist-display-field-list" {
  source = "./regist-display-field-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "delete-display-field-list" {
  source = "./delete-display-field-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-admin-role" {
  source = "./get-admin-role"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "update-admin-role" {
  source = "./update-admin-role"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-tenant-options" {
  source = "./get-tenant-options"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "update-tenant-options" {
  source = "./update-tenant-options"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}
module "get-csv-field-list" {
  source = "./get-csv-field-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "update-csv-field-list" {
  source = "./update-csv-field-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-static-page-list" {
  source = "./get-static-page-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "add-static-page" {
  source = "./add-static-page"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "delete-static-page" {
  source = "./delete-static-page"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "edit-static-page" {
  source = "./edit-static-page"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-static-page" {
  source = "./get-static-page"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-item-fields" {
  source = "./get-item-fields"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-inquiry-chat" {
  source = "./get-inquiry-chat"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "regist-inquiry-chat" {
  source = "./regist-inquiry-chat"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "exchange-display-inquiry-chat" {
  source = "./exchange-display-inquiry-chat"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-ext-link-options" {
  source = "./get-ext-link-options"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "upsert-ext-link-options" {
  source = "./upsert-ext-link-options"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "get-item-mapping-list" {
  source = "./get-item-mapping-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "regist-item-mapping-list" {
  source = "./regist-item-mapping-list"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "search-members" {
  source = "./search-members"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}

module "import-category-tree-csv-file" {
  source = "./import-category-tree-csv-file"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "import-category-node-csv-file" {
  source = "./import-category-node-csv-file"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-categories" {
  source = "./get-categories"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
}
