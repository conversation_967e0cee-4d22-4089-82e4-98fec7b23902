const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool(process.env.PGHOST);

const {
  AdminEnableUserCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider');

const cognitoClient = new CognitoIdentityProviderClient({});

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('update-member-status');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      if (params.member_no === null) {
        // 承認初回
        console.log('GET EMAIL BY REQUEST NO');
        const getEmailByRequestSql =
          'SELECT member_request_no, free_field FROM t_member_request WHERE member_request_no = $1 AND delete_flag = 0';
        return pool
          .rlsQuery(Base.extractTenantId(e),getEmailByRequestSql, [params.member_request_no])
          .then(result => {
            return Promise.resolve(result[0]?.free_field?.email);
          });
      }
      // 承認2回目以降
      console.log('GET EMAIL BY MEMBER NO');
      const getEmailByMemberSql =
        'SELECT member_no, free_field FROM m_member WHERE member_no = $1 AND delete_flag = 0';
      return pool
        .rlsQuery(Base.extractTenantId(e),getEmailByMemberSql, [params.member_no])
        .then(result => {
          return Promise.resolve(result[0]?.free_field?.email);
        });
    })
    .then(getEmail => {
      // ステータス更新「承認」の場合はメールアドレスの重複チェックを行う
      if (params.status === 1) {
        console.log('CHECK DUPLICATED EMAIL');
        const sql_params = [Base.extractTenantId(e), getEmail];
        return pool
          .rlsQuery(Base.extractTenantId(e),'SELECT * FROM "f_get_member_by_email"($1,$2);', sql_params)
          .then(members => {
            console.log('members: ', members);
            const err = {
              status: 400,
              message: Define.MESSAGE.E100024,
            };
            if (params.member_no !== null) {
              if (
                typeof members !== 'undefined' &&
                members !== null &&
                members.length > 1
              ) {
                return Promise.reject(err);
              }
            } else if (
              typeof members !== 'undefined' &&
              members !== null &&
              members.length > 0
            ) {
              return Promise.reject(err);
            }
            return Promise.resolve();
          });
      }
      return Promise.resolve();
    })
    .then(() => {
      console.log('GET ORIGINAL MEMBER INFO');

      // Get member's current information
      const sql = Define.QUERY.GET_MEMBER_FUNCTION;

      // Get tenant_no
      const tenant_no = Base.extractTenantId(e);
      const sql_params = [tenant_no, params.member_request_no];

      console.log(`sql = ${JSON.stringify(sql)}`);
      console.log(`sql_params = ${JSON.stringify(sql_params)}`);

      return new Promise((resolve, reject) => {
        pool
          .rlsQuery(Base.extractTenantId(e),sql, sql_params)
          .then(members => {
            console.log(`members: ${JSON.stringify(members)}`);
            if (
              typeof members === 'undefined' ||
              members === null ||
              members.length === 0
            ) {
              const err = {
                status: 500,
                message: Define.MESSAGE.E000138,
              };
              return reject(err);
            }
            return resolve(members[0]);
          })
          .catch(error => {
            return reject(error);
          });
      });
    })
    .then(member => {
      console.log(`member: ${JSON.stringify(member)}`);
      // Update member status
      const tenant_no = Base.extractTenantId(e);
      console.log(`tenant_no = ${tenant_no}`);
      const admin_no = Base.extractAdminNo(e);
      const admin_name = Base.extractAdminName(e);
      console.log(`admin_no = ${admin_no}, admin_name = ${admin_name}`);

      let sql = null;
      let sql_params = null;

      const member_no = member.member_no;
      // 承認初回のみ
      if (
        params.status === 1 &&
        (typeof member_no === 'undefined' || member_no === null)
      ) {
        // 会員申請テーブルのみに存在する
        sql = Define.QUERY.UPDATE_MEMBER_STATUS_APPROVE_FUNCTION;
        sql_params = [
          tenant_no,
          params.member_request_no,
          admin_name,
          member.status,
          params.status,
          admin_no,
        ];
      } else {
        // 既に会員テーブルに存在した
        // 非承認・停止・退会
        // または承認２回目以上の時
        sql = Define.QUERY.UPDATE_MEMBER_STATUS_OTHERS_FUNCTION;
        sql_params = [
          tenant_no,
          params.member_request_no,
          admin_name,
          member.status,
          params.status,
          admin_no,
        ];
      }

      console.log(`status = ${params.status}`);
      console.log(`sql = ${JSON.stringify(sql)}`);
      console.log(`sql_params = ${JSON.stringify(sql_params)}`);

      return new Promise((resolve, reject) => {
        pool
          .rlsQuery(Base.extractTenantId(e),sql, sql_params)
          .then(result => {
            if (result.length > 0) {
              const id = Object.values(result[0])[0];
              return resolve({
                member_id: id,
                member,
              });
            }
            return reject();
          })
          .catch(error => {
            return reject(error);
          });
      });
    })
    .then(data => {
      const member_id = data.member_id;
      const member = data.member;
      const status = params.status;

      console.log(`member_id = ${member_id}`);
      console.log(`status = ${status}`);
      console.log(`member email = ${member.free_field.email}`);

      // Enable Cognito user when status is approved (status === 1)
      if (status === 1) {
        console.log('ENABLE COGNITO USER');
        const email = member.free_field.email;
        const command = new AdminEnableUserCommand({
          UserPoolId: process.env.AUCTION_USER_POOL_ID_IN_ADMIN,
          Username: email,
        });

        return cognitoClient.send(command)
          .then(() => {
            console.log(`✓ Cognito user ${email} enabled successfully`);
            return Promise.resolve(data);
          })
          .catch(error => {
            console.error(`Failed to enable Cognito user ${email}:`, error);
            // Continue with the flow even if Cognito operation fails
            return Promise.resolve(data);
          });
      }

      return Promise.resolve(data);
    })
    .then(data => {
      console.log('SEND EMAIL');

      const member_id = data.member_id;
      const member = data.member;
      const status = params.status;

      console.log(`member_id = ${member_id}`);
      console.log(`status = ${status}`);
      console.log(`member email = ${member.free_field.email}`);

      // 承認と退会のみメール送信
      // 承認メールについては1度しか送らない
      if (
        (status !== 1 && status !== 9) ||
        (status === 1 &&
          typeof member.member_id !== 'undefined' &&
          member.member_id !== null &&
          member.member_id.length > 0)
      ) {
        return Promise.resolve(member_id);
      }

      return Promise.resolve()
        .then(() => {
          // Get email setting from constant
          const sqlParams = [
            [
              'EMAIL_MEMBER_REQUEST_APPROVED_FOR_MEMBER',
              'EMAIL_MEMBER_WITHDRAWAL_FOR_ADMIN',
              'EMAIL_COMMON_FOOTER',
              'EMAIL_FROM',
            ],
            Base.extractTenantId(e),
            'ja',
          ];
          return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_CONSTANTS_BY_KEYS, sqlParams);
        })
        .then(constants => {
          let mail = {};
          let content = '';
          let sender = null;
          let receivers = null;
          const bcc = [];

          const mailFrom =
            constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 || null;
          if (status === 1) {
            // 承認
            mail =
              constants.find(
                x => x.key_string === 'EMAIL_MEMBER_REQUEST_APPROVED_FOR_MEMBER'
              ) || {};
            const footer =
              constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') || {};

            // Footer：{0}
            content = Common.format(mail.value4, [footer.value4 || '']);
            // Sender
            sender = mailFrom ? `"${mailFrom}"<${mail.value2}>` : mail.value2;
            // Receiver
            receivers = [member.free_field.email];
          } else if (status === 9) {
            // 退会
            mail =
              constants.find(
                x => x.key_string === 'EMAIL_MEMBER_WITHDRAWAL_FOR_ADMIN'
              ) || {};
            content = Common.format(mail.value4, [
              member.free_field.companyName || '',
              member.member_id || '',
              member.free_field.companyName || '',
              member.free_field.tel || '',
              member.free_field.email || '',
            ]);
            // Sender
            sender = mailFrom ? `"${mailFrom}"<${mail.value2}>` : mail.value2;
            // Receiver
            const recv_emails = mail.value3;
            receivers = recv_emails ? recv_emails.split(',') : null;
          }

          // Title
          const title = mail.value1;

          console.log(`title: ${title}`);
          console.log(`sender: ${sender}`);
          console.log(`receivers: ${JSON.stringify(receivers)}`);
          console.log(`content: ${JSON.stringify(content)}`);

          return Common.sendMailBySES(title, content, sender, receivers, bcc);
        });
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
