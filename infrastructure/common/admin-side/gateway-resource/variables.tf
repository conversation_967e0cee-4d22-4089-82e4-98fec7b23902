variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "allow_origin" {
  description = "allow_origin"
}

variable "s3-bucket-arn" {
  description = "File upload bucket arn"
}

variable "s3-bucket-id" {
  description = "File upload bucket id"
}

variable "aws_api_gateway_rest_api_gateway_id" {
  description = "Aws api gateway rest api"
  default     = ""
}

variable root_resource_id {
  description = "Aws api gateway root resource id"
  default     = ""
}

variable aws_api_gateway_rest_api_gateway_execution_arn {
  description = "Aws api gateway execution arn"
  default     = ""
}

variable prefix_function_name {
  description = "prefix_function_name"
  default     = "admin"
}

variable "lambda_subnet_ids" {
  description = "lambda_subnet_ids"
  default       = []
}

variable "lambda_security_group_id" {
  description = "lambda_security_group_id"
  default     = ""
}

variable "lambda_global_environment_variables" {
  description = "lambda_global_environment_variables"
  default     = {}
}

variable "slack-notification-lambda-arn" {
  description = "slack-notification-lambda-arn"
}

variable "common_lambda_layer" {
  description = "common_lambda_layer"
}

variable "sent-html-email-lambda-arn" {
  description = "sent-html-email-lambda-arn"
}

variable "sent-html-notice-email-lambda-arn" {
  description = "sent-html-notice-email-lambda-arn"
}

variable "zip-transfer-lambda-arn" {
  description = "zip-transfer-lambda-arn"
}

variable "get-first-frame-mp4-lambda-arn" {
  description = "get-first-frame-mp4-lambda-arn"
}

variable "send-notification-email-lambda-arn" {
  description = "send-notification-email-lambda-arn"
}

variable "push_change_items_lambda_arn" {
  description = "push_change_items_lambda_arn"
}

variable "cognito_client_id" {
  description = "ID of Cognito App Client"
  type        = string
}

variable "cognito_user_pool_arn" {
  description = "arn of cognito user pool, use for cognito authorizer"
  type        = string
}

variable "cognito_user_pool_id" {
  description = "ID of cognito user pool, used to validate JWT tokens"
  type        = string
}

variable "auction_cognito_user_pool_id" {
  description = "ID of auction cognito user pool, used for member management operations"
  type        = string
}
