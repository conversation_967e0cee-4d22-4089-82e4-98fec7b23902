module "gateway-resource" {
  source = "./gateway-resource"
  project_name = var.project_name
  environment = var.environment
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = aws_api_gateway_rest_api.gateway.id
  root_resource_id = aws_api_gateway_rest_api.gateway.root_resource_id
  aws_api_gateway_rest_api_gateway_execution_arn = aws_api_gateway_rest_api.gateway.execution_arn
  allow_origin = var.environment == "prod" ? format("https://%s", var.domain_name) : "*"
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  common_lambda_layer = var.common_lambda_layer
  sent-html-email-lambda-arn = var.sent-html-email-lambda-arn
  sent-html-notice-email-lambda-arn = var.sent-html-notice-email-lambda-arn
  zip-transfer-lambda-arn = var.zip-transfer-lambda-arn
  get-first-frame-mp4-lambda-arn = var.get-first-frame-mp4-lambda-arn
  send-notification-email-lambda-arn = var.send-notification-email-lambda-arn
  push_change_items_lambda_arn = var.push_change_items_lambda_arn
  cognito_user_pool_arn = var.cognito_user_pool_arn
  cognito_user_pool_id = var.cognito_user_pool_id
  cognito_client_id = var.cognito_client_id
  auction_cognito_user_pool_id = var.auction_cognito_user_pool_id
}

module "domain" {
  source = "./domain"
  project_name = var.project_name
  environment = var.environment
  external_domain_name = var.external_domain_name
  record_name = var.record_name
  s3-bucket-bucket = var.s3-bucket-bucket
  bucket_regional_domain_name = var.bucket_regional_domain_name
  aws_api_gateway_rest_api_name = aws_api_gateway_rest_api.gateway.name
  api_gateway_deployment_invoke_url = aws_api_gateway_stage.gateway_stage.invoke_url
  api_gateway_deployment_stage_name = aws_api_gateway_stage.gateway_stage.stage_name
  profile_name = var.profile_name
  admin_basic_auth_enable = var.admin_basic_auth_enable
  admin_tenants = var.admin_tenants
  admin_custom_domains = var.admin_custom_domains
  admin_acm_domain_name = var.admin_acm_domain_name
}
